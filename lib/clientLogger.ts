import pino from 'pino';

// Determine if we are in a development environment and if we are in the browser
const isDev = process.env.NODE_ENV === 'development';
const isBrowser = typeof window !== 'undefined';

// Base Pino options
const baseOptions = {
  level: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',
  // Redact sensitive information
  redact: {
    paths: [
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
};

// Conditional configuration for browser vs. server
const clientLogger = pino({
  ...baseOptions,
  browser: {
    // In the browser, always output as object (JSON)
    asObject: true,
    // No transport for browser, let the browser console handle it
  },
  // For server-side usage of this logger (e.g., during SSR), use pino-pretty in dev
  transport: isDev && !isBrowser ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  } : undefined,
});

export default clientLogger;