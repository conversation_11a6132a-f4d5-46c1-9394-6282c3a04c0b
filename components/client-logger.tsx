'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import clientLogger from '@/lib/clientLogger';

/**
 * Client-side logging component that tracks page transitions
 * This component must be marked with 'use client' to use React hooks
 */
export function ClientLogger() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const url = `${pathname}?${searchParams}`;
    clientLogger.info({ url }, 'Page transition');
  }, [pathname, searchParams]);

  // This component doesn't render anything
  return null;
}
