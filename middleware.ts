import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";
import { asyncLocalStorage } from "./lib/logger";
import logger from "./lib/logger";

export async function middleware(request: NextRequest) {
  const requestId = crypto.randomUUID();
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const path = request.nextUrl.pathname;
  const method = request.method;

  return asyncLocalStorage.run(
    { requestId, userAgent, path, method },
    async () => {
      try {
        logger.info({ req: { method, url: path, id: requestId } }, 'Incoming request');

        // Redirect root path to /admin
        if (path === "/") {
          return NextResponse.redirect(new URL("/admin", request.url));
        }

        // Get authentication token
        const token = await getToken({
          req: request,
          secret: process.env.NEXTAUTH_SECRET,
        });

        // Handle login page access - redirect to admin if already authenticated
        if (path === "/admin/login") {
          if (token) {
            return NextResponse.redirect(new URL("/admin", request.url));
          }
          return NextResponse.next();
        }

        // Check if the path starts with /admin and protect it
        if (path.startsWith("/admin")) {
          // Redirect to login if not authenticated
          if (!token) {
            const url = new URL("/admin/login", request.url);
            url.searchParams.set("callbackUrl", encodeURI(request.url));
            return NextResponse.redirect(url);
          }
        }

        return NextResponse.next();
      } catch (error) {
        logger.error({ err: error, req: { method, url: path, id: requestId } }, 'Error in middleware');
        return NextResponse.next(); // Allow the request to continue to the error boundary
      }
    }
  );
}

export const config = {
  matcher: ["/", "/admin/:path*"],
};
