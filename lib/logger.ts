import { pino, Logger, transport } from 'pino';
import { AsyncLocalStorage } from 'async_hooks';

// Define a type for the request context
interface RequestContext {
  requestId?: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  path?: string;
  method?: string;
}

// Initialize AsyncLocalStorage for request-scoped context
export const asyncLocalStorage = new AsyncLocalStorage<RequestContext>();

// Determine if we are in a development environment
const isDev = process.env.NODE_ENV === 'development';

// Pino configuration for production (JSON output)
const productionConfig = {
  level: process.env.LOG_LEVEL || 'info',
  formatters: {
    level: (label: string) => ({ level: label.toUpperCase() }),
  },
  // Redact sensitive information
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      'res.headers["set-cookie"]',
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
  timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
};

// Pino configuration for development (human-readable output with pino-pretty)
const developmentConfig = {
  level: process.env.LOG_LEVEL || 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
      messageFormat: '{msg} {req.method} {req.url} {req.id} {context}',
    },
  },
  // Redact sensitive information in dev as well, though pino-pretty might display it differently
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      'res.headers["set-cookie"]',
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
};

// Create the base logger instance
const baseLogger = pino(isDev ? developmentConfig : productionConfig);

// Extend the base logger to include request-scoped context
const logger = new Proxy(baseLogger, {
  get(target, property, receiver) {
    const store = asyncLocalStorage.getStore();
    const childLogger = store ? target.child(store) : target;

    // If the property is a logging method (e.g., 'info', 'error'), bind it to the childLogger
    if (typeof childLogger[property as keyof Logger] === 'function') {
      return (childLogger[property as keyof Logger] as Function).bind(childLogger);
    }
    // Otherwise, return the property directly from the childLogger
    return Reflect.get(childLogger, property, receiver);
  },
}) as Logger; // Cast to Logger to maintain type safety

export default logger;

// Type definitions for custom logger properties (if any)
declare module 'pino' {
  interface PinoLogger {
    withContext(context: RequestContext): Logger;
  }
}