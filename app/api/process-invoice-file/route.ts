import { NextRequest, NextResponse } from 'next/server';
import { InvoiceData } from '@/lib/types/invoice';
import { processInvoiceFile } from '@/lib/api/external-service';
import { validateApi<PERSON>ey, ApiKeyValidationResult } from '@/lib/api/api-key-middleware';
import {
  createErrorResponse,
  handleColorDetection,
  createProcessingLog,
  updateInvoiceLogWithResults,
  updateLogWithError,
  createApiResponse,
  updateLogWithApiKeyId,
  parseFormDataWithFormidable
} from '@/lib/api/shared-invoice-processing';

// Define a type for the file from FormData
interface FormDataFile extends File {
  arrayBuffer(): Promise<ArrayBuffer>;
}

// Use Node.js runtime for stream and fs support
export const runtime = 'nodejs';

// Disable body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * Process and match a standard invoice file upload
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  // Validate API key
  const validation = await validateApiKey(req);

  // Handle API key validation
  if (validation && 'error' in validation && validation.error) {
    // Return error response if validation failed
    return validation.error;
  }

  try {
    const { fields, files } = await parseFormDataWithFormidable(req);
    const pdfFile = files['file'];

    // Validate required parameters using shared utility
    if (!pdfFile) {
      return NextResponse.json(
        {
          error: 'Missing file parameter',
          results: { success: false }
        },
        { status: 400 }
      );
    }
    const requiredFields: Array<{ key: string; name: string; type: 'string' | 'number' | 'date' }> = [
      { key: 'vendor_name', name: 'vendor_name', type: 'string' },
      { key: 'invoice_number', name: 'invoice_number', type: 'string' },
      { key: 'invoice_date', name: 'invoice_date', type: 'date' },
      { key: 'invoice_amount', name: 'invoice_amount', type: 'number' },
      { key: 'vat_amount', name: 'vat_amount', type: 'number' },
    ];
    const { validateInvoiceFields } = await import('@/lib/api/validate-invoice-fields');
    // Convert parsed fields to a plain object for validation and parse numbers
    const formValues: Record<string, unknown> = {};
    for (const field of requiredFields) {
      const value = fields[field.key];
      if (field.type === 'number' && typeof value === 'string') {
        formValues[field.key] = parseFloat(value);
      } else {
        formValues[field.key] = value;
      }
    }

    const validationError = validateInvoiceFields(formValues, requiredFields);

    if (validationError) {
      return NextResponse.json(
        {
          error: validationError,
          results: { success: false }
        },
        { status: 400 }
      );
    }

    // Extract input data from parsed fields
    const inputData: InvoiceData = {
      vendorName: fields['vendor_name'] || undefined,
      invoiceNo: fields['invoice_number'] || undefined,
      invoiceDate: fields['invoice_date'] || undefined,
      invoiceAmount: fields['invoice_amount'] ?
        parseFloat(fields['invoice_amount']) : undefined,
      vatAmount: fields['vat_amount'] ?
        parseFloat(fields['vat_amount']) : undefined,
    };


    // Parse date string to a valid Date object if possible
    let documentDate: Date | null = null;
    if (inputData.invoiceDate && typeof inputData.invoiceDate === 'string') {
      // Try different date formats
      const dateStr = inputData.invoiceDate;

      // Try to parse DD/MM/YY format
      if (/^\d{1,2}\/\d{1,2}\/\d{2,4}$/.test(dateStr)) {
        const [day, month, year] = dateStr.split('/').map(Number);
        const fullYear = year < 100 ? 2000 + year : year;
        documentDate = new Date(fullYear, month - 1, day);
      }
      // Try to parse DD-MM-YYYY format
      else if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(dateStr)) {
        const [day, month, year] = dateStr.split('-').map(Number);
        documentDate = new Date(year, month - 1, day);
      }
      // Try standard ISO format (YYYY-MM-DD)
      else if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(dateStr)) {
        documentDate = new Date(dateStr);
      }

      // Validate the date
      if (documentDate && isNaN(documentDate.getTime())) {
        documentDate = null;
      }
    }

    // Use the buffer from the parsed file
    const buffer = pdfFile.buffer;

    // Create a log entry for this request
    const log = await createProcessingLog(
      'invoice',
      pdfFile.filename,
      buffer.length,
      inputData
    );

    // If we have an API key ID, update the log with it
    if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
      await updateLogWithApiKeyId(log.id, validation.apiKeyId);
    }

    try {
      // Process the invoice using the external service
      const externalResponse = await processInvoiceFile(buffer, inputData);

      // Detect if the invoice is colored and which pages have color
      const colorDetection = await handleColorDetection(buffer);

      // Use shared matching utility for robust field comparison
      const { matchFields } = await import('@/lib/api/field-matching');
      const fieldTypes = {
        vendor_name: 'string',
        invoice_number: 'string',
        invoice_date: 'date',
        invoice_amount: 'number',
        vat_amount: 'number',
      };
      const { fields: enhancedFields, summary: enhancedSummary } = matchFields(
        {
          vendor_name: inputData.vendorName,
          invoice_number: inputData.invoiceNo,
          invoice_date: inputData.invoiceDate,
          invoice_amount: inputData.invoiceAmount,
          vat_amount: inputData.vatAmount,
        } as Record<string, string | number | Date>,
        externalResponse.results.fields,
        fieldTypes
      );
      const results = {
        ...externalResponse.results,
        fields: enhancedFields,
        summary: enhancedSummary,
        is_colored: colorDetection.is_colored,
        color_pages: colorDetection.color_pages,
        total_pages: colorDetection.total_pages
      };

      // Update the log with the results
      if (externalResponse) {
        await updateInvoiceLogWithResults(log.id, externalResponse, colorDetection, results);

        // Return the response in the format expected by the client
        return NextResponse.json(createApiResponse(externalResponse, log.id, results));
      }

      // If externalResponse is undefined, return a generic success response
      return NextResponse.json({
        processing_id: `success-${Date.now()}`,
        results,
        log_id: log?.id || `log-${Date.now()}`
      });

    } catch (error) {
      // Update the log with the error if log exists
      if (log && log.id) {
        await updateLogWithError(log.id, error);

        // Return error response with log ID
        return NextResponse.json(
          createErrorResponse(error, log.id),
          { status: 500 }
        );
      }

      // If log doesn't exist, return a generic error response
      if (process.env.NODE_ENV !== 'test') {
        logger.error('Error processing invoice file (no log):', error);
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return NextResponse.json(
        {
          error: errorMessage,
          results: { success: false },
          processing_id: `error-${Date.now()}`
        },
        { status: 500 }
      );
    }

  } catch (error) {
    logger.error('❌ Error in main try block:', error);
    logger.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');

    if (process.env.NODE_ENV !== 'test') {
      logger.error('Error processing invoice file:', error);
    }
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Create a generic error response without a log ID
    return NextResponse.json(
      {
        error: errorMessage,
        results: { success: false },
        processing_id: `error-${Date.now()}`
      },
      { status: 500 }
    );
  }
}
