3.12.7) blue-lagoon:invoice-matching fajar$ pnpm update eslint-config-next@latest
 WARN  deprecated @types/bcryptjs@3.0.0: This is a stub types definition. bcryptjs provides its own type definitions, so you do not need this installed.
Downloading prisma@6.8.2: 6.21 MB/6.21 MB, done
Downloading @prisma/client@6.8.2: 6.02 MB/6.02 MB, done
 WARN  10 deprecated subdependencies found: are-we-there-yet@2.0.0, gauge@3.0.2, glob@7.1.6, glob@7.2.3, inflight@1.0.6, lodash.get@4.4.2, lodash.isequal@4.5.0, node-domexception@1.0.0, npmlog@5.0.1, rimraf@3.0.2
Packages: +142 -2
+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++--
Progress: resolved 1051, reused 885, downloaded 66, added 142, done
 WARN  Issues with peer dependencies found
.
├─┬ @testing-library/react 15.0.7
│ ├── ✕ unmet peer @types/react@^18.0.0: found 19.1.5
│ ├── ✕ unmet peer react@^18.0.0: found 19.1.0
│ ├── ✕ unmet peer react-dom@^18.0.0: found 19.1.0
│ └─┬ @types/react-dom 18.3.7
│   └── ✕ unmet peer @types/react@^18.0.0: found 19.1.5
├─┬ react-day-picker 8.10.1
│ ├── ✕ unmet peer react@"^16.8.0 || ^17.0.0 || ^18.0.0": found 19.1.0
│ └── ✕ unmet peer date-fns@"^2.28.0 || ^3.0.0": found 4.1.0
├─┬ swagger-ui-react 5.22.0
│ ├── ✕ unmet peer react@">=16.8.0 <19": found 19.1.0
│ ├── ✕ unmet peer react-dom@">=16.8.0 <19": found 19.1.0
│ ├─┬ react-copy-to-clipboard 5.1.0
│ │ └── ✕ unmet peer react@"^15.3.0 || 16 || 17 || 18": found 19.1.0
│ ├─┬ react-debounce-input 3.3.0
│ │ └── ✕ unmet peer react@"^15.3.0 || 16 || 17 || 18": found 19.1.0
│ ├─┬ react-inspector 6.0.2
│ │ └── ✕ unmet peer react@"^16.8.4 || ^17.0.0 || ^18.0.0": found 19.1.0
│ └─┬ swagger-client 3.35.3
│   └─┬ @swagger-api/apidom-reference 1.0.0-beta.39
│     └─┬ @swagger-api/apidom-parser-adapter-api-design-systems-json 1.0.0-beta.39
│       └─┬ @swagger-api/apidom-parser-adapter-json 1.0.0-beta.39
│         └─┬ tree-sitter-json 0.24.8
│           └── ✕ unmet peer tree-sitter@^0.21.1: found 0.22.1
├─┬ vaul 0.9.9
│ ├── ✕ unmet peer react@"^16.8 || ^17.0 || ^18.0": found 19.1.0
│ └── ✕ unmet peer react-dom@"^16.8 || ^17.0 || ^18.0": found 19.1.0
└─┬ @vitest/coverage-v8 3.1.4
  └── ✕ unmet peer vitest@3.1.4: found 1.6.1

dependencies:
- @prisma/client 6.7.0
+ @prisma/client 6.8.2
- prisma 6.7.0
+ prisma 6.8.2
- react-hook-form 7.56.3
+ react-hook-form 7.56.4
- swagger-ui-react 5.21.0
+ swagger-ui-react 5.22.0
- zod 3.24.4
+ zod 3.25.28

devDependencies:
- @types/node 22.15.17
+ @types/node 22.15.21
- @types/react 19.1.3
+ @types/react 19.1.5
- @types/react-dom 19.1.3
+ @types/react-dom 19.1.5
- @vitejs/plugin-react 4.4.1
+ @vitejs/plugin-react 4.5.0
- @vitest/coverage-v8 3.1.3
+ @vitest/coverage-v8 3.1.4
- msw 2.8.1
+ msw 2.8.4

╭ Warning ───────────────────────────────────────────────────────────────────────────────────╮
│                                                                                            │
│   Ignored build scripts: @prisma/client, @prisma/engines, msw, prisma.                     │
│   Run "pnpm approve-builds" to pick which dependencies should be allowed to run scripts.   │
│                                                                                            │
╰────────────────────────────────────────────────────────────────────────────────────────────╯

Done in 16.1s using pnpm v10.8.1

=============

 pnpm add parse-multipart-data
 WARN  deprecated @types/bcryptjs@3.0.0: This is a stub types definition. bcryptjs provides its own type definitions, so you do not need this installed.
 WARN  9 deprecated subdependencies found: are-we-there-yet@2.0.0, gauge@3.0.2, glob@7.1.6, inflight@1.0.6, lodash.get@4.4.2, lodash.isequal@4.5.0, node-domexception@1.0.0, npmlog@5.0.1, rimraf@3.0.2
Packages: +1
+
Progress: resolved 1074, reused 951, downloaded 1, added 1, done
 WARN  Issues with peer dependencies found
.
├─┬ @vitest/coverage-v8 3.1.3
│ └── ✕ unmet peer vitest@3.1.3: found 1.6.1
├─┬ @testing-library/react 15.0.7
│ ├── ✕ unmet peer @types/react@^18.0.0: found 19.1.3
│ ├── ✕ unmet peer react@^18.0.0: found 19.1.0
│ ├── ✕ unmet peer react-dom@^18.0.0: found 19.1.0
│ └─┬ @types/react-dom 18.3.7
│   └── ✕ unmet peer @types/react@^18.0.0: found 19.1.3
├─┬ swagger-ui-react 5.21.0
│ ├── ✕ unmet peer react@">=16.8.0 <19": found 19.1.0
│ ├── ✕ unmet peer react-dom@">=16.8.0 <19": found 19.1.0
│ ├─┬ react-copy-to-clipboard 5.1.0
│ │ └── ✕ unmet peer react@"^15.3.0 || 16 || 17 || 18": found 19.1.0
│ ├─┬ react-debounce-input 3.3.0
│ │ └── ✕ unmet peer react@"^15.3.0 || 16 || 17 || 18": found 19.1.0
│ ├─┬ react-inspector 6.0.2
│ │ └── ✕ unmet peer react@"^16.8.4 || ^17.0.0 || ^18.0.0": found 19.1.0
│ └─┬ swagger-client 3.35.0
│   └─┬ @swagger-api/apidom-reference 1.0.0-beta.36
│     └─┬ @swagger-api/apidom-parser-adapter-api-design-systems-json 1.0.0-beta.36
│       └─┬ @swagger-api/apidom-parser-adapter-json 1.0.0-beta.36
│         └─┬ tree-sitter-json 0.24.8
│           └── ✕ unmet peer tree-sitter@^0.21.1: found 0.22.1
├─┬ vaul 0.9.9
│ ├── ✕ unmet peer react@"^16.8 || ^17.0 || ^18.0": found 19.1.0
│ └── ✕ unmet peer react-dom@"^16.8 || ^17.0 || ^18.0": found 19.1.0
└─┬ react-day-picker 8.10.1
  ├── ✕ unmet peer react@"^16.8.0 || ^17.0.0 || ^18.0.0": found 19.1.0
  └── ✕ unmet peer date-fns@"^2.28.0 || ^3.0.0": found 4.1.0



 WARN  deprecated @types/bcryptjs@3.0.0: This is a stub types definition. bcryptjs provides its own type definitions, so you do not need this installed.

   ╭───────────────────────────────────────────────╮
   │                                               │
   │      Update available! 10.8.1 → 10.11.0.      │
   │     Changelog: https://pnpm.io/v/10.11.0      │
   │   To update, run: corepack use pnpm@10.11.0   │
   │                                               │
   ╰───────────────────────────────────────────────╯

 WARN  9 deprecated subdependencies found: are-we-there-yet@2.0.0, gauge@3.0.2, glob@7.1.6, inflight@1.0.6, lodash.get@4.4.2, lodash.isequal@4.5.0, node-domexception@1.0.0, npmlog@5.0.1, rimraf@3.0.2
Packages: +3
+++
Progress: resolved 899, reused 798, downloaded 3, added 3, done
 WARN  Issues with peer dependencies found
.
├─┬ @vitest/coverage-v8 3.1.3
│ └── ✕ unmet peer vitest@3.1.3: found 1.6.1
├─┬ @testing-library/react 15.0.7
│ ├── ✕ unmet peer @types/react@^18.0.0: found 19.1.3
│ ├── ✕ unmet peer react@^18.0.0: found 19.1.0
│ ├── ✕ unmet peer react-dom@^18.0.0: found 19.1.0
│ └─┬ @types/react-dom 18.3.7
│   └── ✕ unmet peer @types/react@^18.0.0: found 19.1.3
├─┬ react-day-picker 8.10.1
│ ├── ✕ unmet peer react@"^16.8.0 || ^17.0.0 || ^18.0.0": found 19.1.0
│ └── ✕ unmet peer date-fns@"^2.28.0 || ^3.0.0": found 4.1.0
├─┬ swagger-ui-react 5.21.0
│ ├── ✕ unmet peer react@">=16.8.0 <19": found 19.1.0
│ ├── ✕ unmet peer react-dom@">=16.8.0 <19": found 19.1.0
│ ├─┬ react-copy-to-clipboard 5.1.0
│ │ └── ✕ unmet peer react@"^15.3.0 || 16 || 17 || 18": found 19.1.0
│ ├─┬ react-debounce-input 3.3.0
│ │ └── ✕ unmet peer react@"^15.3.0 || 16 || 17 || 18": found 19.1.0
│ ├─┬ react-inspector 6.0.2
│ │ └── ✕ unmet peer react@"^16.8.4 || ^17.0.0 || ^18.0.0": found 19.1.0
│ └─┬ swagger-client 3.35.0
│   └─┬ @swagger-api/apidom-reference 1.0.0-beta.36
│     └─┬ @swagger-api/apidom-parser-adapter-api-design-systems-json 1.0.0-beta.36
│       └─┬ @swagger-api/apidom-parser-adapter-json 1.0.0-beta.36
│         └─┬ tree-sitter-json 0.24.8
│           └── ✕ unmet peer tree-sitter@^0.21.1: found 0.22.1
└─┬ vaul 0.9.9
  ├── ✕ unmet peer react@"^16.8 || ^17.0 || ^18.0": found 19.1.0
  └── ✕ unmet peer react-dom@"^16.8 || ^17.0 || ^18.0": found 19.1.0

dependencies:
+ fuzzball 2.2.2

Done in 2.4s using pnpm v10.8.1



