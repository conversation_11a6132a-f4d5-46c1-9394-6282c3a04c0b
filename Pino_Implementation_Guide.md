# Pino Logging Implementation Guide for Next.js 15 (App Router) with TypeScript

This guide provides a comprehensive approach to integrating <PERSON><PERSON>, a highly performant Node.js logger, into a Next.js 15 application using the App Router and TypeScript. It covers optimal configuration, custom formatting, integration with Next.js features, centralized logger management, security best practices, and real-world examples.

## 1. Installation

First, install the necessary packages:

```bash
pnpm install pino pino-pretty
```

- `pino`: The core logging library.
- `pino-pretty`: A transport for pretty-printing Pino logs in development environments.

## 2. Optimal Pino Configuration (`lib/logger.ts`)

Create a centralized logger configuration file, e.g., [`lib/logger.ts`](lib/logger.ts). This file will handle environment-specific configurations (JSON for production, pretty for development), PII redaction, and integration with `AsyncLocalStorage` for request-scoped context.

```typescript
// lib/logger.ts
import { pino, Logger, transport } from 'pino';
import { AsyncLocalStorage } from 'async_hooks';

// Define a type for the request context
interface RequestContext {
  requestId?: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  path?: string;
  method?: string;
}

// Initialize AsyncLocalStorage for request-scoped context
export const asyncLocalStorage = new AsyncLocalStorage<RequestContext>();

// Determine if we are in a development environment
const isDev = process.env.NODE_ENV === 'development';

// Pino configuration for production (JSON output)
const productionConfig = {
  level: process.env.LOG_LEVEL || 'info',
  formatters: {
    level: (label: string) => ({ level: label.toUpperCase() }),
  },
  // Redact sensitive information
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      'res.headers.set-cookie',
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
  timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
};

// Pino configuration for development (human-readable output with pino-pretty)
const developmentConfig = {
  level: process.env.LOG_LEVEL || 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
      messageFormat: '{msg} {req.method} {req.url} {req.id} {context}',
    },
  },
  // Redact sensitive information in dev as well, though pino-pretty might display it differently
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      'res.headers.set-cookie',
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
};

// Create the base logger instance
const baseLogger = pino(isDev ? developmentConfig : productionConfig);

// Extend the base logger to include request-scoped context
const logger = new Proxy(baseLogger, {
  get(target, property, receiver) {
    const store = asyncLocalStorage.getStore();
    const childLogger = store ? target.child(store) : target;

    // If the property is a logging method (e.g., 'info', 'error'), bind it to the childLogger
    if (typeof childLogger[property as keyof Logger] === 'function') {
      return (childLogger[property as keyof Logger] as Function).bind(childLogger);
    }
    // Otherwise, return the property directly from the childLogger
    return Reflect.get(childLogger, property, receiver);
  },
}) as Logger; // Cast to Logger to maintain type safety

export default logger;

// Type definitions for custom logger properties (if any)
declare module 'pino' {
  interface PinoLogger {
    withContext(context: RequestContext): Logger;
  }
}
```

**Explanation:**

- **`RequestContext` Interface:** Defines the structure for request-specific data.
- **`asyncLocalStorage`:** This is crucial for centralized logger management with request-scoped context. It allows you to store data (like `requestId`, `userId`) that is accessible throughout the entire request lifecycle, even across asynchronous operations.
- **Conditional Configuration:** The logger is configured differently based on `process.env.NODE_ENV`. In development, `pino-pretty` provides human-readable output. In production, it outputs structured JSON, ideal for log aggregation systems.
- **`redact`:** This feature is used to prevent sensitive information (PII) from being logged. You define paths within your log objects that should be censored.
- **`timestamp`:** Customizes the timestamp format for production logs.
- **Proxy for Context:** The `logger` is a `Proxy` around the `baseLogger`. When a logging method is called, it checks `asyncLocalStorage` for a stored context and creates a child logger with that context. This ensures that all logs within a request automatically include the request's context.

## 3. Client-Side Logger (`lib/clientLogger.ts`)

For client-side logging (e.g., page transitions, user interactions), create a separate logger. This logger will behave differently in the browser environment.

```typescript
// lib/clientLogger.ts
import pino from 'pino';

// Determine if we are in a development environment and if we are in the browser
const isDev = process.env.NODE_ENV === 'development';
const isBrowser = typeof window !== 'undefined';

// Base Pino options
const baseOptions = {
  level: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',
  // Redact sensitive information
  redact: {
    paths: [
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
};

// Conditional configuration for browser vs. server
const clientLogger = pino({
  ...baseOptions,
  browser: {
    // In the browser, always output as object (JSON)
    asObject: true,
    // No transport for browser, let the browser console handle it
  },
  // For server-side usage of this logger (e.g., during SSR), use pino-pretty in dev
  transport: isDev && !isBrowser ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  } : undefined,
});

export default clientLogger;
```

**Explanation:**

- **`isBrowser` Check:** Differentiates between client-side and server-side execution.
- **`browser` Option:** Configures how Pino behaves in the browser. `asObject: true` ensures JSON output, which is then typically handled by the browser's developer console.
- **Conditional `transport`:** `pino-pretty` is only applied if the logger is used in a Node.js environment (e.g., during SSR for initial page loads) and in development mode.

## 4. Integration with Next.js Middleware (`middleware.ts`)

The Next.js middleware is the ideal place to capture request context and initialize `AsyncLocalStorage`.

```typescript
// middleware.ts
import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";
import { asyncLocalStorage } from "./lib/logger";
import logger from "./lib/logger";

export async function middleware(request: NextRequest) {
  const requestId = crypto.randomUUID();
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const path = request.nextUrl.pathname;
  const method = request.method;

  return asyncLocalStorage.run(
    { requestId, userAgent, path, method },
    async () => {
      try {
        logger.info({ req: { method, url: path, id: requestId } }, 'Incoming request');

        // Redirect root path to /admin
        if (path === "/") {
          return NextResponse.redirect(new URL("/admin", request.url));
        }

        // Get authentication token
        const token = await getToken({
          req: request,
          secret: process.env.NEXTAUTH_SECRET,
        });

        // Handle login page access - redirect to admin if already authenticated
        if (path === "/admin/login") {
          if (token) {
            return NextResponse.redirect(new URL("/admin", request.url));
          }
          return NextResponse.next();
        }

        // Check if the path starts with /admin and protect it
        if (path.startsWith("/admin")) {
          // Redirect to login if not authenticated
          if (!token) {
            const url = new URL("/admin/login", request.url);
            url.searchParams.set("callbackUrl", encodeURI(request.url));
            return NextResponse.redirect(url);
          }
        }

        return NextResponse.next();
      } catch (error) {
        logger.error({ err: error, req: { method, url: path, id: requestId } }, 'Error in middleware');
        return NextResponse.next(); // Allow the request to continue to the error boundary
      }
    }
  );
}

export const config = {
  matcher: ["/", "/admin/:path*"],
};
```

**Explanation:**

- **`requestId`:** A unique ID generated for each request, crucial for tracing.
- **`asyncLocalStorage.run()`:** This wraps the entire middleware logic. Any `logger` calls within this block will automatically inherit the `requestId`, `userAgent`, `path`, and `method`.
- **Error Handling:** A `try...catch` block ensures that any errors in the middleware are logged.

## 5. Integration with Next.js Error Boundaries (`app/global-error.tsx`)

For global error handling in the App Router, you can use the `global-error.tsx` file.

```typescript
// app/global-error.tsx
"use client";

import * as Sentry from "@sentry/nextjs";
import NextError from "next/error";
import { useEffect } from "react";
import logger from "../lib/logger"; // Adjust path as necessary

export default function GlobalError({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    Sentry.captureException(error);
    logger.error({ err: error }, 'Global error caught by error boundary');
  }, [error]);

  return (
    <html>
      <body>
        {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
        <NextError statusCode={0} />
      </body>
    </html>
  );
}
```

**Explanation:**

- **`useEffect`:** Captures the error and logs it using the server-side `logger`. This ensures that unhandled errors are properly recorded.

## 6. Real-World Examples

### 6.1. API Routes (`app/api/admin/api-keys/route.ts`)

Integrate the logger into your API routes for detailed request and error logging.

```typescript
// app/api/admin/api-keys/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/db';
import crypto from 'crypto';
import logger from '@/lib/logger';

/**
 * Generate a secure random API key with a prefix and hash
 * @returns An object containing the full API key and its hash
 */
export function generateApiKey(): { fullKey: string, hashedKey: string } {
  // Generate a key with a prefix for identification
  const keyId = 'ki_' + crypto.randomBytes(8).toString('hex');
  const keySecret = crypto.randomBytes(32).toString('hex');
  const fullKey = `${keyId}.${keySecret}`;

  // Hash the key for storage
  const hashedKey = crypto.createHash('sha256').update(fullKey).digest('hex');

  return { fullKey, hashedKey };
}

// Export default object for mocking in tests
export default { generateApiKey };

/**
 * Get all API keys
 */
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      logger.warn('Unauthorized attempt to get API keys');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all API keys
    const apiKeys = await prisma.apiKey.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        lastUsedAt: true,
        isActive: true,
        createdBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    logger.info('Successfully retrieved API keys');
    return NextResponse.json({ apiKeys });
  } catch (error) {
    logger.error({ err: error }, 'Error getting API keys');
    return NextResponse.json(
      { error: 'Error getting API keys' },
      { status: 500 }
    );
  }
}

/**
 * Create a new API key
 */
export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      logger.warn('Unauthorized attempt to create API key');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { name, description } = body;

    // Validate request body
    if (!name) {
      logger.warn('Attempt to create API key with missing name');
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Generate a new API key and its hash
    const { fullKey, hashedKey } = generateApiKey();

    // Create a new API key - store only the hash
    const apiKey = await prisma.apiKey.create({
      data: {
        name,
        description,
        keyHash: hashedKey, // Store only the hash in the database
        createdById: session.user.id,
      },
    });

    logger.info({ apiKeyId: apiKey.id, name: apiKey.name }, 'Successfully created API key');
    // Return the full key to the user - this is the ONLY time the full key is available
    return NextResponse.json({
      id: apiKey.id,
      name: apiKey.name,
      description: apiKey.description,
      key: fullKey, // Return the full key to the user
      createdAt: apiKey.createdAt,
    }, { status: 201 });
  } catch (error) {
    logger.error({ err: error }, 'Error creating API key');
    return NextResponse.json(
      { error: 'Error creating API key' },
      { status: 500 }
    );
  }
}
```

**Explanation:**

- **Import `logger`:** The server-side logger is imported and used for all logging within the API route.
- **Contextual Logging:** Because of `AsyncLocalStorage` in the middleware, logs here will automatically include the `requestId` and other request context.
- **Error and Success Logging:** `logger.error` is used for exceptions, and `logger.info` for successful operations, providing clear visibility into API behavior.
- **`logger.warn`:** Used for scenarios like unauthorized access or invalid input, indicating potential issues that aren't necessarily errors but warrant attention.

### 6.2. Page Transitions (`app/layout.tsx`)

For client-side page transitions, use the `clientLogger` in your root layout.

```typescript
// app/layout.tsx
"use client"; // This is a client component

import type { Metadata } from 'next'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { SessionProvider } from '@/components/session-provider'
import { Toaster } from '@/components/ui/toaster'
import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import clientLogger from '@/lib/clientLogger';

export const metadata: Metadata = {
  title: 'Invoice Processing System',
  description: 'Process and match invoices and tax invoices',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const url = `${pathname}?${searchParams}`;
    clientLogger.info({ url }, 'Page transition');
  }, [pathname, searchParams]);

  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <SessionProvider>
            {children}
            <Toaster />
          </SessionProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
```

**Explanation:**

- **`"use client"`:** Marks the component as a Client Component, allowing the use of React Hooks like `useEffect`.
- **`usePathname` and `useSearchParams`:** Next.js hooks to get the current URL path and query parameters.
- **`useEffect`:** Logs an `info` message with the current URL whenever the `pathname` or `searchParams` change, indicating a page transition.

### 6.3. Server Actions (Conceptual Example)

Server Actions run on the server, so they should use the main `logger` from [`lib/logger.ts`](lib/logger.ts). The `AsyncLocalStorage` context from the middleware will automatically be available.

```typescript
// app/some-page/actions.ts (Example Server Action file)
"use server";

import logger from '@/lib/logger';
import { revalidatePath } from 'next/cache';

export async function submitForm(formData: FormData) {
  try {
    const name = formData.get('name');
    const email = formData.get('email');

    // Simulate a database operation
    logger.info({ name, email }, 'Processing form submission via Server Action');

    // Simulate an error
    if (!name) {
      throw new Error('Name is required');
    }

    // ... perform actual data mutations ...

    logger.info('Form submission successful');
    revalidatePath('/some-page'); // Revalidate cache if data changes
    return { success: true };
  } catch (error) {
    logger.error({ err: error, formData: Object.fromEntries(formData.entries()) }, 'Error in Server Action');
    return { success: false, error: (error as Error).message };
  }
}
```

**Explanation:**

- **`"use server"`:** Marks the file as a Server Action.
- **Import `logger`:** The server-side logger is used.
- **Automatic Context:** The `requestId` and other context from the middleware will automatically be attached to these logs.
- **Error Handling:** `try...catch` blocks are essential for robust error logging in Server Actions.

## 7. Security Best Practices: PII Redaction and Log Filtering

As demonstrated in [`lib/logger.ts`](lib/logger.ts) and [`lib/clientLogger.ts`](lib/clientLogger.ts), Pino's `redact` option is the primary mechanism for PII redaction.

- **`redact.paths`:** An array of strings representing paths to properties that should be censored. Pino supports dot notation for nested properties (e.g., `user.password`).
- **`redact.censor`:** The value to replace redacted data with (e.g., `[REDACTED]`).

**Log Filtering:**

- **`level`:** Set the `level` option in your Pino configuration (via `LOG_LEVEL` and `NEXT_PUBLIC_LOG_LEVEL` environment variables) to control the minimum severity of logs that are outputted. For example, setting `LOG_LEVEL=warn` will only show `warn`, `error`, and `fatal` logs.
- **Custom Serializers:** For more complex filtering or transformation of log data, you can use Pino's `serializers` option. This allows you to define functions that transform specific objects before they are logged.

## 8. Environment Variable Templates (`.env.local.example`)

Provide clear environment variables for configuring log levels.

```ini
# .env.local.example

# Logging Configuration
# Server-side log level (e.g., 'info', 'debug', 'warn', 'error', 'fatal', 'silent')
# Default is 'info'
LOG_LEVEL=info

# Client-side log level (e.g., 'info', 'debug', 'warn', 'error', 'fatal', 'silent')
# Default is 'info'
NEXT_PUBLIC_LOG_LEVEL=info
```

**Note:** `NEXT_PUBLIC_` prefix is required for client-side environment variables in Next.js.

## 9. Performance Benchmarks: `console.log` vs. Pino

Pino is designed for high performance. While direct benchmarking within this environment is not possible, here's a conceptual comparison and how you might benchmark it yourself:

**Theoretical Comparison:**

-   **`console.log`:**
    -   **Pros:** Simple, built-in, no external dependencies.
    -   **Cons:** Synchronous (can block the event loop), limited formatting options, no structured logging, no built-in redaction or context management. Performance degrades significantly under high load due to synchronous I/O.
-   **Pino:**
    -   **Pros:** Asynchronous by default (non-blocking), highly performant, structured JSON output, extensible with transports, built-in redaction, child loggers for context.
    -   **Cons:** Requires a small setup, adds a dependency.

**Benchmarking Approach (Self-Execution):**

To benchmark, you would typically create a simple Node.js script (or a Next.js API route under load) and log a large number of messages using both `console.log` and Pino.

**Example Benchmark Script (Conceptual):**

```typescript
// benchmark.ts (Run with `ts-node benchmark.ts`)
import { performance } from 'perf_hooks';
import pino from 'pino';

const ITERATIONS = 100000; // Number of log operations

// --- Pino Benchmark ---
const pinoLogger = pino({ level: 'silent' }); // Set to silent to avoid console output overhead during benchmark
const pinoStart = performance.now();
for (let i = 0; i < ITERATIONS; i++) {
  pinoLogger.info({ count: i, message: 'Pino log message' }, 'Test log');
}
const pinoEnd = performance.now();
console.log(`Pino logging ${ITERATIONS} messages took: ${pinoEnd - pinoStart} ms`);

// --- Console.log Benchmark ---
const consoleStart = performance.now();
for (let i = 0; i < ITERATIONS; i++) {
  console.log('Console log message', { count: i, message: 'Console log message' });
}
const consoleEnd = performance.now();
console.log(`Console.log ${ITERATIONS} messages took: ${consoleEnd - consoleStart} ms`);

// --- Console.log with JSON (closer to Pino's output) ---
const consoleJsonStart = performance.now();
for (let i = 0; i < ITERATIONS; i++) {
  console.log(JSON.stringify({ level: 'info', time: Date.now(), msg: 'Console JSON log message', count: i }));
}
const consoleJsonEnd = performance.now();
console.log(`Console.log (JSON) ${ITERATIONS} messages took: ${consoleJsonEnd - consoleJsonStart} ms`);
```

**Expected Results:**

You would typically observe that Pino, especially when configured for production (JSON output without `pino-pretty` transport), is significantly faster than `console.log` for a large volume of logs, particularly because Pino writes asynchronously to `stdout`. When `pino-pretty` is involved, the performance overhead increases due to the parsing and formatting, but it's still generally more efficient for structured logging than manually constructing complex `console.log` statements.

## Conclusion

By following this guide, you can implement a robust, performant, and maintainable logging solution in your Next.js 15 application using Pino, leveraging its features for structured logging, context management, and security.