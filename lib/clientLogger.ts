import pino from 'pino';

// Base Pino options
const baseOptions = {
  level: process.env.NEXT_PUBLIC_LOG_LEVEL || 'info',
  // Redact sensitive information
  redact: {
    paths: [
      'user.password',
      'data.password',
      'email',
      'address',
      'phone',
    ],
    censor: '[REDACTED]',
  },
};

// Conditional configuration for browser vs. server
const clientLogger = pino({
  ...baseOptions,
  browser: {
    // In the browser, always output as object (JSON)
    asObject: true,
    // No transport for browser, let the browser console handle it
  },
  // Simplified configuration to avoid worker thread issues
  // Remove transport configuration entirely to prevent worker issues
});

export default clientLogger;