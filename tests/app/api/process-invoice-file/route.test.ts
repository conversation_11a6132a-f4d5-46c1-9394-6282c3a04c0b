import { describe, it, expect, vi, beforeEach } from 'vitest'
import { POST } from '@/app/api/process-invoice-file/route'
import { MockNextRequest, createMockInvoiceFormData, MockFile } from '../../../utils'
import '../../../mocks/prisma'
import '../../../mocks/color-detection'

// Mock the API key middleware
vi.mock('@/lib/api/api-key-middleware', () => ({
  validateApiKey: vi.fn().mockResolvedValue({ apiKeyId: 'mock-api-key-id' })
}))

// Mock the File object in the global scope
global.File = MockFile

// Mock the external service module
vi.mock('@/lib/api/external-service', () => ({
  processInvoiceFile: vi.fn().mockResolvedValue({
    processing_id: 'test-processing-id',
    document_processing_log_id: 'test-document-processing-log-id',
    results: {
      success: true,
      summary: {
        total_fields: 5,
        matched: 3,
        mismatched: 1,
        not_found: 1
      },
      fields: {
        vendor_name: { input_value: 'Test Vendor', ocr_value: 'Test Vendor', status: 'matched' },
        invoice_number: { input_value: 'INV-123', ocr_value: 'INV-123', status: 'matched' },
        invoice_date: { input_value: '2023-01-01', ocr_value: '2023-01-01', status: 'matched' },
        invoice_amount: { input_value: '1000', ocr_value: '1000.00', status: 'mismatched' },
        vat_amount: { input_value: '100', ocr_value: '', status: 'not_found' }
      }
    }
  })
}))

// Mock the shared invoice processing module
vi.mock('@/lib/api/shared-invoice-processing', () => ({
  createErrorResponse: vi.fn((error, logId) => ({
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: { total_fields: 0, matched: 0, mismatched: 0, not_found: 0 },
      fields: {}
    },
    error: typeof error === 'string' ? error : (error instanceof Error ? error.message : 'Unknown error'),
    log_id: logId
  })),
  parseDate: vi.fn(),
  handleColorDetection: vi.fn().mockResolvedValue({
    is_colored: false,
    color_pages: [],
    total_pages: 3,
    confidence: 'high',
    method: 'Pixel-by-Pixel RGB Analysis'
  }),
  createProcessingLog: vi.fn().mockResolvedValue({
    id: 'mock-log-id',
    requestType: 'invoice',
    fileName: 'test.pdf',
    fileSize: 1024,
    processingStatus: 'processing'
  }),
  updateLogWithError: vi.fn().mockResolvedValue({}),
  createApiResponse: vi.fn((externalResponse, logId, results) => ({
    processing_id: externalResponse.processing_id,
    results,
    log_id: logId
  })),
  updateInvoiceLogWithResults: vi.fn().mockResolvedValue({}),
  updateLogWithApiKeyId: vi.fn().mockResolvedValue({}),
  parseFormDataWithFormidable: vi.fn().mockImplementation(async (req) => {
    // Check if this is the empty form data test case
    if (req.formDataValue && req.formDataValue.entries && Array.from(req.formDataValue.entries()).length === 0) {
      return { fields: {}, files: {} };
    }
    // For normal cases, return mock data
    return {
      fields: {
        vendor_name: 'Test Vendor',
        invoice_number: 'INV-123',
        invoice_date: '2023-01-01',
        invoice_amount: '1000',
        vat_amount: '100'
      },
      files: {
        file: {
          buffer: Buffer.from('mock pdf content'),
          filename: 'test.pdf',
          mimetype: 'application/pdf',
          size: 1024
        }
      }
    };
  })
}))

// Import after mocks are set up
import { parseFormDataWithFormidable } from '@/lib/api/shared-invoice-processing'

describe('Process Invoice File API', () => {
  beforeEach(() => {
    vi.resetAllMocks()

    // Reset and configure parseFormDataWithFormidable mock
    vi.mocked(parseFormDataWithFormidable).mockResolvedValue({
      fields: {
        vendor_name: 'Test Vendor',
        invoice_number: 'INV-123',
        invoice_date: '2023-01-01',
        invoice_amount: '1000',
        vat_amount: '100'
      },
      files: {
        file: {
          buffer: Buffer.from('mock pdf content'),
          filename: 'test.pdf',
          mimetype: 'application/pdf',
          size: 1024
        }
      }
    })
  })

  it('should process an invoice file successfully', async () => {
    // Create mock form data
    const formData = createMockInvoiceFormData({
      fileName: 'test-invoice.pdf',
      vendorName: 'Test Vendor',
      invoiceNo: 'INV-123',
      invoiceDate: '2023-01-01',
      invoiceAmount: 1000,
      vatAmount: 100
    })

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice-file', {
      formData
    })

    // Call the API endpoint
    const response = await POST(request as any)

    // For now, we'll just check that the function runs without errors
    expect(response).toBeDefined()
  })

  it('should return an error when no file is provided', async () => {
    // Mock parseFormDataWithFormidable to return empty files for this test
    vi.mocked(parseFormDataWithFormidable).mockResolvedValueOnce({
      fields: {},
      files: {}
    })

    // Create empty form data
    const formData = new FormData()

    // Create mock request
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice-file', {
      formData
    })

    // Call the API endpoint
    const response = await POST(request as any)
    const data = await response.json()

    // Verify the response
    expect(response.status).toBe(400)
    expect(data).toHaveProperty('error', 'Missing file parameter')
    expect(data).toHaveProperty('results.success', false)
  })

  it('should handle processing errors', async () => {
    // Mock the external service to throw an error
    const processInvoiceFile = vi.spyOn(await import('@/lib/api/external-service'), 'processInvoiceFile')
    processInvoiceFile.mockRejectedValue(new Error('Processing failed'))

    // Create mock form data with all required fields
    const formData = createMockInvoiceFormData()
    formData.set('vendor_name', 'Test Vendor')
    formData.set('invoice_number', 'INV-123')
    formData.set('invoice_date', '2023-01-01')
    formData.set('invoice_amount', '1000')
    formData.set('vat_amount', '100')

    // Create mock request with the form data
    const request = new MockNextRequest('POST', 'http://localhost:3000/api/process-invoice-file')
    request.formData = vi.fn().mockResolvedValue(formData)

    // Call the API endpoint
    const response = await POST(request as any)
    const data = await response.json()

    // Verify the response
    expect(response.status).toBe(500)
    expect(data).toHaveProperty('error')
    expect(data).toHaveProperty('results.success', false)
  })
})
